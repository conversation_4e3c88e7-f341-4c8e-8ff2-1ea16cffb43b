// bible-quiz-07-22-2025/data/quiz-types.ts

export interface QuizType {
  name: string;
  slug: string;
  description: string;
  category: 'testament' | 'gospel' | 'wisdom' | 'character' | 'thematic' | 'chapter';
  icon: string;
}

export const quizTypes: QuizType[] = [
  // Testament-based
  { 
    name: 'Old Testament Quizzes', 
    slug: 'old-testament', 
    description: 'Test your knowledge of the Old Testament books and stories',
    category: 'testament',
    icon: 'scroll'
  },
  { 
    name: 'New Testament Quizzes', 
    slug: 'new-testament', 
    description: 'Explore the New Testament through interactive quizzes',
    category: 'testament',
    icon: 'cross'
  },

  // Gospel-specific
  { 
    name: 'Gospel Quizzes', 
    slug: 'gospels', 
    description: 'Focus on the four Gospels: <PERSON>, <PERSON>, <PERSON>, and <PERSON>',
    category: 'gospel',
    icon: 'book'
  },

  // Wisdom Literature
  { 
    name: 'Proverbs & Psalms Quizzes', 
    slug: 'proverbs-psalms', 
    description: 'Dive into the wisdom literature of Proverbs and Psalms',
    category: 'wisdom',
    icon: 'wisdom'
  },

  // Character-based
  { 
    name: '<PERSON> Quizzes', 
    slug: 'paul', 
    description: 'Learn about the Apostle Paul\'s life and teachings',
    category: 'character',
    icon: 'person'
  },
  { 
    name: '<PERSON> Quizzes', 
    slug: 'moses', 
    description: 'Explore the life and leadership of Moses',
    category: 'character',
    icon: 'person'
  },
  { 
    name: 'David Quizzes', 
    slug: 'david', 
    description: 'Study King David\'s life, psalms, and reign',
    category: 'character',
    icon: 'person'
  },
  { 
    name: 'Jesus Quizzes', 
    slug: 'jesus', 
    description: 'Focus on the life, teachings, and ministry of Jesus Christ',
    category: 'character',
    icon: 'person'
  },

  // Thematic
  { 
    name: 'Faith Quizzes', 
    slug: 'faith', 
    description: 'Explore biblical teachings about faith and trust in God',
    category: 'thematic',
    icon: 'heart'
  },
  { 
    name: 'Prayer Quizzes', 
    slug: 'prayer', 
    description: 'Learn about prayer from biblical examples and teachings',
    category: 'thematic',
    icon: 'pray'
  },
  { 
    name: 'Salvation Quizzes', 
    slug: 'salvation', 
    description: 'Understand God\'s plan of salvation through Scripture',
    category: 'thematic',
    icon: 'cross'
  },

  // Chapter-specific examples
  { 
    name: 'Genesis 1 Quiz', 
    slug: 'genesis-1', 
    description: 'Test your knowledge of the creation account',
    category: 'chapter',
    icon: 'chapter'
  },
  { 
    name: 'Romans 8 Quiz', 
    slug: 'romans-8', 
    description: 'Explore this powerful chapter about life in the Spirit',
    category: 'chapter',
    icon: 'chapter'
  },
  { 
    name: 'John 3 Quiz', 
    slug: 'john-3', 
    description: 'Study Jesus\' conversation with Nicodemus',
    category: 'chapter',
    icon: 'chapter'
  }
];

export interface ThematicTopic {
  name: string;
  slug: string;
  description: string;
  keyVerses: string[];
  relatedBooks: string[];
}

export const thematicTopics: ThematicTopic[] = [
  {
    name: 'Jesus\' Parables',
    slug: 'jesus-parables',
    description: 'Stories Jesus told to teach spiritual truths',
    keyVerses: ['Matthew 13:3', 'Luke 15:3', 'Mark 4:2'],
    relatedBooks: ['Matthew', 'Mark', 'Luke']
  },
  {
    name: 'Ten Commandments',
    slug: 'ten-commandments',
    description: 'God\'s moral law given to Moses',
    keyVerses: ['Exodus 20:1-17', 'Deuteronomy 5:4-21'],
    relatedBooks: ['Exodus', 'Deuteronomy']
  },
  {
    name: 'End Times',
    slug: 'end-times',
    description: 'Biblical prophecy about the last days',
    keyVerses: ['Revelation 21:1', '1 Thessalonians 4:16', 'Matthew 24:30'],
    relatedBooks: ['Revelation', 'Daniel', 'Matthew', '1 Thessalonians']
  },
  {
    name: 'Spiritual Gifts',
    slug: 'spiritual-gifts',
    description: 'Gifts given by the Holy Spirit to believers',
    keyVerses: ['1 Corinthians 12:4', 'Romans 12:6', 'Ephesians 4:11'],
    relatedBooks: ['1 Corinthians', 'Romans', 'Ephesians']
  },
  {
    name: 'Sermon on the Mount',
    slug: 'sermon-on-the-mount',
    description: 'Jesus\' famous teaching in Matthew 5-7',
    keyVerses: ['Matthew 5:3', 'Matthew 6:9', 'Matthew 7:7'],
    relatedBooks: ['Matthew']
  },
  {
    name: 'Fruits of the Spirit',
    slug: 'fruits-of-the-spirit',
    description: 'Character qualities produced by the Holy Spirit',
    keyVerses: ['Galatians 5:22-23'],
    relatedBooks: ['Galatians']
  },
  {
    name: 'Armor of God',
    slug: 'armor-of-god',
    description: 'Spiritual protection for believers',
    keyVerses: ['Ephesians 6:10-18'],
    relatedBooks: ['Ephesians']
  }
];

export const getQuizTypeBySlug = (slug: string) => quizTypes.find(type => type.slug === slug);
export const getTopicBySlug = (slug: string) => thematicTopics.find(topic => topic.slug === slug);
export const getQuizTypesByCategory = (category: string) => quizTypes.filter(type => type.category === category);
