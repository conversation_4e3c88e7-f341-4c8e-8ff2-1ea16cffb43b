// bible-quiz-07-22-2025/data/bible-books.ts

export interface BibleBook {
  name: string;
  slug: string;
  testament: 'Old' | 'New';
  chapters: number;
  description: string;
  keyThemes: string[];
}

export const bibleBooks: BibleBook[] = [
  // Old Testament
  { name: '<PERSON>', slug: 'genesis', testament: 'Old', chapters: 50, description: 'The book of beginnings - creation, fall, and early history', keyThemes: ['Creation', 'Fall', 'Covenant', 'Patriarchs'] },
  { name: 'Exodus', slug: 'exodus', testament: 'Old', chapters: 40, description: 'Israel\'s deliverance from Egypt and the giving of the Law', keyThemes: ['Deliverance', 'Ten Commandments', 'Tabernacle', 'Moses'] },
  { name: '<PERSON><PERSON>', slug: 'leviticus', testament: 'Old', chapters: 27, description: 'Laws for worship, sacrifice, and holy living', keyThemes: ['Holiness', 'Sacrifice', 'Priesthood', 'Purity'] },
  { name: 'Numbers', slug: 'numbers', testament: 'Old', chapters: 36, description: 'Israel\'s wilderness wanderings and preparation for the Promised Land', keyThemes: ['Wilderness', 'Faith', 'Rebellion', 'Census'] },
  { name: 'Deuteronomy', slug: 'deuteronomy', testament: 'Old', chapters: 34, description: 'Moses\' final speeches and the renewal of the covenant', keyThemes: ['Covenant', 'Obedience', 'Love', 'Law'] },
  { name: 'Joshua', slug: 'joshua', testament: 'Old', chapters: 24, description: 'The conquest and settlement of the Promised Land', keyThemes: ['Conquest', 'Faith', 'Leadership', 'Promise'] },
  { name: 'Judges', slug: 'judges', testament: 'Old', chapters: 21, description: 'The cycle of sin, oppression, and deliverance in early Israel', keyThemes: ['Cycle', 'Judges', 'Apostasy', 'Deliverance'] },
  { name: 'Ruth', slug: 'ruth', testament: 'Old', chapters: 4, description: 'A story of loyalty, love, and redemption', keyThemes: ['Loyalty', 'Redemption', 'Love', 'Kinsman-redeemer'] },
  { name: '1 Samuel', slug: '1-samuel', testament: 'Old', chapters: 31, description: 'The transition from judges to kings - Samuel, Saul, and David', keyThemes: ['Kingship', 'Anointing', 'Obedience', 'Heart'] },
  { name: '2 Samuel', slug: '2-samuel', testament: 'Old', chapters: 24, description: 'David\'s reign as king of Israel', keyThemes: ['David', 'Kingdom', 'Covenant', 'Sin'] },
  { name: '1 Kings', slug: '1-kings', testament: 'Old', chapters: 22, description: 'Solomon\'s reign and the divided kingdom', keyThemes: ['Wisdom', 'Temple', 'Division', 'Prophets'] },
  { name: '2 Kings', slug: '2-kings', testament: 'Old', chapters: 25, description: 'The decline and fall of Israel and Judah', keyThemes: ['Decline', 'Exile', 'Prophets', 'Judgment'] },
  { name: '1 Chronicles', slug: '1-chronicles', testament: 'Old', chapters: 29, description: 'A priestly perspective on Israel\'s history', keyThemes: ['Genealogy', 'David', 'Temple', 'Worship'] },
  { name: '2 Chronicles', slug: '2-chronicles', testament: 'Old', chapters: 36, description: 'The history of Judah from Solomon to the exile', keyThemes: ['Temple', 'Reform', 'Revival', 'Exile'] },
  { name: 'Ezra', slug: 'ezra', testament: 'Old', chapters: 10, description: 'The return from exile and rebuilding of the temple', keyThemes: ['Return', 'Rebuilding', 'Reform', 'Scripture'] },
  { name: 'Nehemiah', slug: 'nehemiah', testament: 'Old', chapters: 13, description: 'Rebuilding Jerusalem\'s walls and spiritual renewal', keyThemes: ['Rebuilding', 'Leadership', 'Prayer', 'Reform'] },
  { name: 'Esther', slug: 'esther', testament: 'Old', chapters: 10, description: 'God\'s providence in preserving His people', keyThemes: ['Providence', 'Courage', 'Deliverance', 'Identity'] },
  { name: 'Job', slug: 'job', testament: 'Old', chapters: 42, description: 'The problem of suffering and God\'s sovereignty', keyThemes: ['Suffering', 'Faith', 'Sovereignty', 'Wisdom'] },
  { name: 'Psalms', slug: 'psalms', testament: 'Old', chapters: 150, description: 'Songs of worship, praise, and prayer', keyThemes: ['Worship', 'Prayer', 'Praise', 'Trust'] },
  { name: 'Proverbs', slug: 'proverbs', testament: 'Old', chapters: 31, description: 'Practical wisdom for daily living', keyThemes: ['Wisdom', 'Fear of the Lord', 'Character', 'Relationships'] },

  // New Testament
  { name: 'Matthew', slug: 'matthew', testament: 'New', chapters: 28, description: 'Jesus as the promised Messiah and King', keyThemes: ['Messiah', 'Kingdom', 'Fulfillment', 'Teaching'] },
  { name: 'Mark', slug: 'mark', testament: 'New', chapters: 16, description: 'Jesus as the suffering Servant', keyThemes: ['Servant', 'Action', 'Suffering', 'Discipleship'] },
  { name: 'Luke', slug: 'luke', testament: 'New', chapters: 24, description: 'Jesus as the perfect Man and Savior', keyThemes: ['Humanity', 'Compassion', 'Salvation', 'Prayer'] },
  { name: 'John', slug: 'john', testament: 'New', chapters: 21, description: 'Jesus as the Son of God and eternal life', keyThemes: ['Deity', 'Life', 'Love', 'Belief'] },
  { name: 'Acts', slug: 'acts', testament: 'New', chapters: 28, description: 'The birth and growth of the early church', keyThemes: ['Holy Spirit', 'Mission', 'Church', 'Witness'] },
  { name: 'Romans', slug: 'romans', testament: 'New', chapters: 16, description: 'The gospel of God\'s righteousness', keyThemes: ['Gospel', 'Righteousness', 'Faith', 'Grace'] },
  { name: '1 Corinthians', slug: '1-corinthians', testament: 'New', chapters: 16, description: 'Addressing problems in the Corinthian church', keyThemes: ['Unity', 'Spiritual Gifts', 'Love', 'Resurrection'] },
  { name: '2 Corinthians', slug: '2-corinthians', testament: 'New', chapters: 13, description: 'Paul\'s defense of his apostolic ministry', keyThemes: ['Ministry', 'Suffering', 'Comfort', 'Giving'] },
  { name: 'Galatians', slug: 'galatians', testament: 'New', chapters: 6, description: 'Freedom from the law through faith in Christ', keyThemes: ['Freedom', 'Faith', 'Spirit', 'Grace'] },
  { name: 'Ephesians', slug: 'ephesians', testament: 'New', chapters: 6, description: 'Our position and practice in Christ', keyThemes: ['Unity', 'Church', 'Spiritual Warfare', 'Walk'] }
];

export const oldTestamentBooks = bibleBooks.filter(book => book.testament === 'Old');
export const newTestamentBooks = bibleBooks.filter(book => book.testament === 'New');

export const getBookBySlug = (slug: string) => bibleBooks.find(book => book.slug === slug);
