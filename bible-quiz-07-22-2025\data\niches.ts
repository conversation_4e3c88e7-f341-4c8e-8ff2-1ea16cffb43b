// bible-quiz-07-22-2025/data/niches.ts

export interface TechNiche {
  pillar: string;
  niche: string;
  slug: string;
  description: string;
  icon: string;
}

export const techNiches: TechNiche[] = [
  // Energy Assessment
  { pillar: 'Energy Assessment', niche: 'BER Assessors', slug: 'ber-assessors', description: 'Building Energy Rating assessment services', icon: 'energy' },
  { pillar: 'Energy Assessment', niche: 'Home Energy Advisors', slug: 'home-energy-advisors', description: 'Professional home energy consultation', icon: 'energy' },
  { pillar: 'Energy Assessment', niche: 'Retrofit Coordinators', slug: 'retrofit-coordinators', description: 'Energy retrofit project coordination', icon: 'energy' },
  { pillar: 'Energy Assessment', niche: 'Technical Surveyors', slug: 'technical-surveyors', description: 'Technical building surveys and assessments', icon: 'energy' },

  // Solar
  { pillar: 'Solar', niche: 'Solar PV Installation', slug: 'solar-pv-installation', description: 'Solar photovoltaic panel installation services', icon: 'solar' },
  { pillar: 'Solar', niche: 'Solar Battery Storage', slug: 'solar-battery-storage', description: 'Solar energy battery storage solutions', icon: 'solar' },
  { pillar: 'Solar', niche: 'Solar Hot Water Systems', slug: 'solar-hot-water-systems', description: 'Solar thermal hot water heating systems', icon: 'solar' },

  // Heating
  { pillar: 'Heating', niche: 'Heat Pump Installation', slug: 'heat-pump-installation', description: 'Air source and ground source heat pumps', icon: 'heating' },
  { pillar: 'Heating', niche: 'Combi Boiler Replacement', slug: 'combi-boiler-replacement', description: 'Modern combination boiler installation', icon: 'heating' },
  { pillar: 'Heating', niche: 'Underfloor Heating Systems', slug: 'underfloor-heating-systems', description: 'Electric and water underfloor heating', icon: 'heating' },

  // Energy Efficiency
  { pillar: 'Energy Efficiency', niche: 'BER Assessment Services', slug: 'ber-assessment-services', description: 'Building Energy Rating certification', icon: 'energy' },
  { pillar: 'Energy Efficiency', niche: 'Attic Insulation', slug: 'attic-insulation', description: 'Roof and attic insulation installation', icon: 'energy' },
  { pillar: 'Energy Efficiency', niche: 'Cavity Wall Insulation', slug: 'cavity-wall-insulation', description: 'External and cavity wall insulation', icon: 'energy' },

  // Smart Home
  { pillar: 'Smart Home', niche: 'Home Automation Systems', slug: 'home-automation-systems', description: 'Smart home technology and automation', icon: 'smart-home' },
  { pillar: 'Smart Home', niche: 'Smart Heating Controls', slug: 'smart-heating-controls', description: 'Intelligent heating control systems', icon: 'smart-home' },
  { pillar: 'Smart Home', niche: 'Smart Meter Installation', slug: 'smart-meter-installation', description: 'Smart electricity and gas meters', icon: 'smart-home' },

  // Electric
  { pillar: 'Electric', niche: 'EV Home Charger Installation', slug: 'ev-home-charger-installation', description: 'Electric vehicle home charging points', icon: 'electric' },
  { pillar: 'Electric', niche: 'Fuse Board Upgrades', slug: 'fuse-board-upgrades', description: 'Electrical panel and fuse board upgrades', icon: 'electric' },
  { pillar: 'Electric', niche: 'Emergency Backup Systems', slug: 'emergency-backup-systems', description: 'Backup power and generator systems', icon: 'electric' },

  // Network
  { pillar: 'Network', niche: 'Fiber Broadband Setup', slug: 'fiber-broadband-setup', description: 'High-speed fiber internet installation', icon: 'network' },
  { pillar: 'Network', niche: 'Mesh WiFi Installation', slug: 'mesh-wifi-installation', description: 'Whole-home WiFi mesh networks', icon: 'network' },
  { pillar: 'Network', niche: 'Smart TV & Sound Systems', slug: 'smart-tv-sound-systems', description: 'Smart entertainment system setup', icon: 'network' }
];

export const pillarCategories = Array.from(new Set(techNiches.map(n => n.pillar)));

export const getNichesByPillar = (pillar: string) => 
  techNiches.filter(niche => niche.pillar === pillar);

export const getNicheBySlug = (slug: string) => 
  techNiches.find(niche => niche.slug === slug);
