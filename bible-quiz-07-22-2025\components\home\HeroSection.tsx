// bible-quiz-07-22-2025/components/home/<USER>
import Link from 'next/link';
import Image from 'next/image';

export default function HeroSection() {
  return (
    <section className="relative bg-gradient-to-br from-green-50 to-blue-50 py-20 lg:py-32">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="text-center lg:text-left">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
              Test Your{' '}
              <span className="text-green-600">Bible Knowledge</span>{' '}
              with Interactive Quizzes
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl">
              Explore the Bible from Genesis to Revelation with our comprehensive collection 
              of interactive quizzes. Perfect for Bible study, Sunday school, or personal growth.
            </p>
            
            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link
                href="/quiz"
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl"
              >
                Start Quiz Now
                <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
              <Link
                href="/bible-quizzes"
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-green-600 bg-white hover:bg-gray-50 rounded-lg border-2 border-green-600 transition-colors duration-200"
              >
                Browse All Quizzes
              </Link>
            </div>

            {/* Stats */}
            <div className="mt-12 grid grid-cols-3 gap-8 text-center lg:text-left">
              <div>
                <div className="text-3xl font-bold text-green-600">66</div>
                <div className="text-sm text-gray-600">Bible Books</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-green-600">500+</div>
                <div className="text-sm text-gray-600">Quiz Questions</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-green-600">25+</div>
                <div className="text-sm text-gray-600">Topics Covered</div>
              </div>
            </div>
          </div>

          {/* Hero Image */}
          <div className="relative">
            <div className="relative w-full h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-amber-100 via-yellow-50 to-amber-200">
              {/* Bible illustration */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="w-32 h-32 mx-auto mb-4 bg-green-600 rounded-full flex items-center justify-center shadow-lg">
                    <svg className="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800">Holy Bible</h3>
                  <p className="text-gray-600">Interactive Learning</p>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute top-4 left-4 w-8 h-8 bg-yellow-300 rounded-full opacity-60"></div>
              <div className="absolute top-12 right-8 w-6 h-6 bg-orange-300 rounded-full opacity-40"></div>
              <div className="absolute bottom-8 left-8 w-4 h-4 bg-amber-400 rounded-full opacity-50"></div>
              <div className="absolute bottom-4 right-4 w-10 h-10 bg-yellow-200 rounded-full opacity-30"></div>
            </div>
            
            {/* Floating Quiz Card */}
            <div className="absolute -bottom-6 -left-6 bg-white rounded-xl shadow-xl p-6 max-w-xs">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <div className="font-semibold text-gray-900">Genesis 1 Quiz</div>
                  <div className="text-sm text-gray-500">10 questions</div>
                </div>
              </div>
              <div className="text-sm text-gray-600">
                Test your knowledge of the creation account
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
